<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="6dp"
    tools:context=".ui.editevent.EditEventFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Edit Event Form Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_edit_event"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp" >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tv_edit_event_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/edit_event"
                        android:textAppearance="?attr/textAppearanceSubtitle1"
                        android:textColor="?attr/colorOnSurface"
                        android:textStyle="bold" />

                    <!-- Date Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_event_date"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/event_date__required">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_event_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:focusable="false"
                            android:clickable="true" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Title Field -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_event_title"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/event_title__required">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_event_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Submit Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_submit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:text="@string/submit"
                        android:textSize="16sp"
                        app:cornerRadius="8dp" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- Loading Progress Bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
