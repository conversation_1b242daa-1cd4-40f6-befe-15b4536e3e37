<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="2dp"
    android:layout_marginEnd="2dp"
    android:layout_marginTop="4dp"
    android:layout_marginBottom="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- First row: slashed_date_persian (left) and short_uuid (right) with menu button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/text_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="تاریخ"
                android:textSize="11sp"
                android:textColor="?android:attr/textColorTertiary"
                android:gravity="start" />

            <TextView
                android:id="@+id/text_short_uuid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="UUID"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary"
                android:gravity="center" />

            <ImageButton
                android:id="@+id/btn_menu"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_menu_hamburger"
                android:contentDescription="منو"
                android:scaleType="centerInside" />

        </LinearLayout>

        <!-- Second row: title -->
        <TextView
            android:id="@+id/text_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="عنوان"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary"
            android:layout_marginTop="8dp"
            android:gravity="start" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
