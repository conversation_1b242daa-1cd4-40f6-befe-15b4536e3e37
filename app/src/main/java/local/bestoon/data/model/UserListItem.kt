package ir.rahavardit.ariel.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing a user in the system for the users list.
 *
 * @property id The unique identifier for the user.
 * @property username The username of the user.
 * @property firstName The first name of the user.
 * @property lastName The last name of the user.
 * @property email The email address of the user.
 * @property isSuperuser Whether the user has superuser privileges.
 * @property isLimitedAdmin Whether the user has limited admin privileges.
 * @property company The company the user belongs to.
 * @property position The position of the user in their company.
 * @property description The user's description or bio.
 * @property gender The user's gender.
 * @property shortUuid The short UUID of the user.
 */
data class UserListItem(
    val id: Int,
    val username: String,
    @SerializedName("first_name") val firstName: String,
    @SerializedName("last_name") val lastName: String,
    val email: String,
    @SerializedName("is_superuser") val isSuperuser: <PERSON><PERSON><PERSON>,
    @SerializedName("is_limited_admin") val isLimitedAdmin: Boolean,
    val company: String?,
    val position: String?,
    val description: String?,
    val gender: String,
    @SerializedName("short_uuid") val shortUuid: String
)

/**
 * Data class representing a paginated response of users.
 *
 * @property count The total number of users.
 * @property next The URL for the next page of results, or null if there is no next page.
 * @property previous The URL for the previous page of results, or null if there is no previous page.
 * @property results The list of users for the current page.
 */
data class UserListResponse(
    val count: Int,
    val next: String?,
    val previous: String?,
    val results: List<UserListItem>
)
