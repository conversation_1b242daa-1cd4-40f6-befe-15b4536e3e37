package ir.rahavardit.ariel.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing a category in the system.
 *
 * @property id The unique identifier for the category.
 * @property name The name of the category.
 * @property slug The slug of the category.
 * @property persianName The Persian name of the category.
 * @property shortUuid The short UUID of the category.
 */
data class CategoryResponse(
    val id: Int,
    val name: String,
    val slug: String,
    @SerializedName("persian_name") val persianName: String,
    @SerializedName("short_uuid") val shortUuid: String
)

/**
 * Data class representing a paginated response of categories.
 */
data class CategoriesResponse(
    val count: Int,
    val next: String?,
    val previous: String?,
    val results: List<CategoryResponse>
)
