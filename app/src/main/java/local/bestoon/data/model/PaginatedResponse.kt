package ir.rahavardit.ariel.data.model

/**
 * Data class representing a paginated response from the API.
 *
 * @property count The total number of items available.
 * @property next The URL for the next page of results, or null if there is no next page.
 * @property previous The URL for the previous page of results, or null if there is no previous page.
 * @property results The list of items for the current page.
 */
data class PaginatedResponse<T>(
    val count: Int,
    val next: String?,
    val previous: String?,
    val results: List<T>
)
