package ir.rahavardit.ariel.data.model

/**
 * Data class representing a ticket box item in the homepage.
 */
data class TicketBox(
    val type: TicketBoxType,
    val label: String,
    val count: Int? = null,
    val value: String? = null,
    val groupId: Int? = null,
    val foregroundColor: Int? = null,
    val backgroundColor: Int? = null,
    val hasIcon: Boolean = false
)

/**
 * Enum representing different types of ticket boxes.
 */
enum class TicketBoxType {
    ALL_TICKETS,
    TICKET_STATUS,
    GROUP,
    EVENTS,
    PROFILE,
    USERS
}
