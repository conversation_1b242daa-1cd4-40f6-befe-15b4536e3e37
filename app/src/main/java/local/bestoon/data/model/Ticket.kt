package ir.rahavardit.ariel.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * Data class representing a ticket in the system.
 *
 * @property id The unique identifier for the ticket.
 * @property title The title of the ticket.
 * @property author The author of the ticket.
 * @property category The category of the ticket.
 * @property status The status code of the ticket.
 * @property statusDisplay The human-readable status of the ticket.
 * @property statusPersian The Persian human-readable status of the ticket.
 * @property priority The priority code of the ticket.
 * @property priorityDisplay The human-readable priority of the ticket.
 * @property priorityPersian The Persian human-readable priority of the ticket.
 * @property message The message content of the ticket.
 * @property rate The rating of the ticket.
 * @property shortUuid The short UUID of the ticket.
 * @property created The creation timestamp of the ticket.
 * @property createdJalali The creation timestamp in Jalali calendar format.
 * @property updated The last update timestamp of the ticket.
 * @property changes Any changes made to the ticket.
 * @property file The file attached to the ticket.
 * @property fileMimetype The MIME type of the attached file.
 * @property hasFile Whether the ticket has an attached file.
 * @property children Child tickets or responses.
 */
data class Ticket(
    val id: Int,
    val title: String,
    val author: Author,
    val category: Category?, // Made nullable to handle null values from API
    val status: String?,
    @SerializedName("status_display") val statusDisplay: String?,
    @SerializedName("status_persian") val statusPersian: String?,
    val priority: String,
    @SerializedName("priority_display") val priorityDisplay: String?,
    @SerializedName("priority_persian") val priorityPersian: String?,
    val message: String,
    val rate: Int,
    @SerializedName("short_uuid") val shortUuid: String,
    val created: String,
    @SerializedName("created_jalali") val createdJalali: String,
    val updated: String,
    val changes: Map<String, Any>,
    val file: String?,
    @SerializedName("file_mimetype") val fileMimetype: String?,
    @SerializedName("has_file") val hasFile: Boolean,
    val children: List<TicketResponse>?
)

/**
 * Data class representing a ticket response or child ticket.
 *
 * @property id The unique identifier for the response.
 * @property title The title of the response.
 * @property author The author of the response.
 * @property message The message content of the response.
 * @property shortUuid The short UUID of the response.
 * @property created The creation timestamp of the response.
 * @property createdJalali The creation timestamp in Jalali calendar format.
 * @property file The file attached to the response.
 * @property fileMimetype The MIME type of the attached file.
 * @property hasFile Whether the response has an attached file.
 */
data class TicketResponse(
    val id: Int,
    val title: String,
    val author: Author,
    val message: String,
    @SerializedName("short_uuid") val shortUuid: String,
    val created: String,
    @SerializedName("created_jalali") val createdJalali: String,
    val file: String?,
    @SerializedName("file_mimetype") val fileMimetype: String?,
    @SerializedName("has_file") val hasFile: Boolean
)

/**
 * Data class representing an author of a ticket or response.
 *
 * @property id The unique identifier for the author.
 * @property username The username of the author.
 * @property isSuperuser Whether the author has superuser privileges.
 * @property isLimitedAdmin Whether the author has limited admin privileges.
 * @property shortUuid The short UUID of the author.
 */
@Parcelize
data class Author(
    val id: Int,
    val username: String,
    @SerializedName("is_superuser") val isSuperuser: Boolean,
    @SerializedName("is_limited_admin") val isLimitedAdmin: Boolean,
    @SerializedName("short_uuid") val shortUuid: String
) : Parcelable

/**
 * Data class representing a change made to a ticket.
 *
 * @property userId The ID of the user who made the change.
 * @property username The username of the user who made the change.
 * @property action The description of the action performed.
 * @property dateJalali The date of the change in Jalali calendar format.
 */
data class TicketChange(
    @SerializedName("user_id") val userId: Int,
    val username: String,
    val action: String,
    @SerializedName("date_jalali") val dateJalali: String
)

/**
 * Data class representing a ticket category.
 *
 * @property id The unique identifier for the category.
 * @property name The name of the category.
 * @property persianName The Persian name of the category.
 */
data class Category(
    val id: Int,
    val name: String,
    @SerializedName("persian_name") val persianName: String
)
