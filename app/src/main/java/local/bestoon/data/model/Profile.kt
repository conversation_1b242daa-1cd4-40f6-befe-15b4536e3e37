package ir.rahavardit.ariel.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing a user profile in the system.
 *
 * @property id The unique identifier for the user.
 * @property username The username of the user.
 * @property firstName The first name of the user.
 * @property lastName The last name of the user.
 * @property email The email address of the user.
 * @property isSuperuser Whether the user has superuser privileges.
 * @property isLimitedAdmin Whether the user has limited admin privileges.
 * @property company The company the user belongs to.
 * @property position The position of the user in their company.
 * @property description The user's description or bio.
 * @property gender The user's gender.
 * @property shortUuid The short UUID of the user.
 */
data class Profile(
    val id: Int,
    val username: String,
    @SerializedName("first_name") val firstName: String,
    @SerializedName("last_name") val lastName: String,
    val email: String,
    @SerializedName("is_superuser") val isSuperuser: <PERSON><PERSON><PERSON>,
    @SerializedName("is_limited_admin") val isLimitedAdmin: Boolean,
    val company: String?,
    val position: String?,
    val description: String?,
    val gender: String,
    @SerializedName("short_uuid") val shortUuid: String
)
