package ir.rahavardit.ariel.ui.tickets

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.Ticket
import ir.rahavardit.ariel.data.repository.TicketRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the tickets screen that handles ticket-related operations with pagination.
 */
class TicketsViewModel : ViewModel() {

    private val ticketRepository = TicketRepository()

    private val _tickets = MutableLiveData<List<Ticket>>()
    val tickets: LiveData<List<Ticket>> = _tickets

    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    private val _isLoadingMore = MutableLiveData<Boolean>(false)
    val isLoadingMore: LiveData<Boolean> = _isLoadingMore

    private val _error = MutableLiveData<String?>(null)
    val error: LiveData<String?> = _error

    private var nextPageUrl: String? = null
    private var currentTickets = mutableListOf<Ticket>()

    /**
     * Fetches the first page of tickets for the authenticated user.
     *
     * @param token The authentication token.
     * @param status Optional status filter.
     * @param groupId Optional group ID filter.
     */
    fun fetchTickets(token: String, status: String? = null, groupId: Int? = null) {
        _isLoading.value = true
        _error.value = null
        currentTickets.clear()

        viewModelScope.launch {
            try {
                val result = when {
                    status != null -> ticketRepository.getMyTicketsByStatus(token, status)
                    groupId != null -> ticketRepository.getMyTicketsByGroupId(token, groupId)
                    else -> ticketRepository.getMyTickets(token, 15)
                }

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentTickets.addAll(paginatedResponse.results)
                        _tickets.value = currentTickets.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Loads more tickets from the next page if available.
     *
     * @param token The authentication token.
     */
    fun loadMoreTickets(token: String) {
        if (nextPageUrl == null || _isLoadingMore.value == true) {
            return
        }

        _isLoadingMore.value = true

        viewModelScope.launch {
            try {
                val result = ticketRepository.getTicketsFromUrl(token, nextPageUrl!!)

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentTickets.addAll(paginatedResponse.results)
                        _tickets.value = currentTickets.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoadingMore.value = false
            }
        }
    }

    /**
     * Checks if there are more pages to load.
     *
     * @return True if there are more pages, false otherwise.
     */
    fun hasMorePages(): Boolean {
        return nextPageUrl != null
    }

    /**
     * Clears any error messages.
     */
    fun clearError() {
        _error.value = null
    }
}
