package ir.rahavardit.ariel.ui.editevent

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider

class EditEventViewModelFactory(private val token: String) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(EditEventViewModel::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return EditEventViewModel(token) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
