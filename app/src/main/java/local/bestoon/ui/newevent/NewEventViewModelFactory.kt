package ir.rahavardit.ariel.ui.newevent

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider

/**
 * Factory class for creating NewEventViewModel instances with dependencies.
 */
class NewEventViewModelFactory(private val token: String) : ViewModelProvider.Factory {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(NewEventViewModel::class.java)) {
            return NewEventViewModel(token) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
