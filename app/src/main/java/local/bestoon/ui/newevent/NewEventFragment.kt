package ir.rahavardit.ariel.ui.newevent

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.databinding.FragmentNewEventBinding
import ir.rahavardit.ariel.ui.components.JalaliDatePickerDialog
import ir.rahavardit.ariel.utils.JalaliDateUtils
import ir.rahavardit.ariel.utils.PersianUtils

/**
 * Fragment for creating a new event.
 */
class NewEventFragment : Fragment() {

    private var _binding: FragmentNewEventBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: NewEventViewModel
    private lateinit var sessionManager: SessionManager
    private var selectedDate: String? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentNewEventBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())

        val token = sessionManager.getAuthToken()
        if (token.isNullOrEmpty()) {
            Toast.makeText(
                requireContext(),
                getString(R.string.authentication_token_not_found),
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        val factory = NewEventViewModelFactory(token)
        viewModel = ViewModelProvider(this, factory)[NewEventViewModel::class.java]

        setupListeners()
        observeViewModel()
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        // Date picker
        binding.etEventDate.setOnClickListener {
            showDatePicker()
        }

        // Submit button
        binding.btnSubmit.setOnClickListener {
            submitEvent()
        }
    }

    /**
     * Shows the Jalali date picker dialog.
     */
    private fun showDatePicker() {
        val jalaliDatePickerDialog = JalaliDatePickerDialog(
            requireContext(),
            { year, month, day ->
                // Format the selected Jalali date
                selectedDate = JalaliDateUtils.formatJalaliDate(year, month, day)

                // Display the date with Persian numerals
                val displayDate = PersianUtils.convertToPersianNumerals(selectedDate!!)
                binding.etEventDate.setText(displayDate)
                binding.tilEventDate.error = null
            }
        )
        jalaliDatePickerDialog.show()
    }

    /**
     * Validates and submits the event form.
     */
    private fun submitEvent() {
        val title = binding.etEventTitle.text.toString().trim()
        
        // Clear previous errors
        binding.tilEventTitle.error = null
        binding.tilEventDate.error = null

        var hasError = false

        // Validate title
        if (title.isEmpty()) {
            binding.tilEventTitle.error = getString(R.string.please_enter_event_title)
            hasError = true
        }

        // Validate date
        if (selectedDate.isNullOrEmpty()) {
            binding.tilEventDate.error = getString(R.string.please_select_event_date)
            hasError = true
        }

        if (!hasError) {
            // Submit the event with English numerals for the date
            // Ensure the date contains English numerals before sending to API
            val dateWithEnglishNumerals = PersianUtils.convertToEnglishNumerals(selectedDate!!)
            viewModel.createEvent(title, dateWithEnglishNumerals)
        }
    }

    /**
     * Observes ViewModel LiveData.
     */
    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnSubmit.isEnabled = !isLoading
        }

        viewModel.createEventResult.observe(viewLifecycleOwner) { result ->
            result.fold(
                onSuccess = {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.event_created_successfully),
                        Toast.LENGTH_SHORT
                    ).show()
                    
                    // Navigate to a fresh new event page
                    findNavController().navigate(R.id.action_newEventFragment_to_newEventFragment)
                },
                onFailure = { exception ->
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.error_creating_event),
                        Toast.LENGTH_LONG
                    ).show()
                }
            )
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
