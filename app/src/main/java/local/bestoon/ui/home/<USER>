package ir.rahavardit.ariel.ui.home

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.model.HomepageDataResponse
import ir.rahavardit.ariel.data.repository.TransactionRepository
import ir.rahavardit.ariel.data.repository.EventRepository

import kotlinx.coroutines.launch

class HomeViewModel(private val sessionManager: SessionManager) : ViewModel() {

    private val apiService = RetrofitClient.getApiService()
    private val transactionRepository = TransactionRepository()
    private val eventRepository = EventRepository()

    private val _homepageData = MutableLiveData<HomepageDataResponse>()
    val homepageData: LiveData<HomepageDataResponse> = _homepageData

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    private val _successMessage = MutableLiveData<String>()
    val successMessage: LiveData<String> = _successMessage



    /**
     * Loads homepage data from the API with optional filter parameters.
     */
    fun loadHomepageData() {
        val token = sessionManager.getAuthToken()
        if (token == null) {
            Log.e("HomeViewModel", "No auth token available")
            _errorMessage.value = "Authentication token not available"
            return
        }

        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = ""

            try {
                // Get saved filter parameters
                val chosenYear = sessionManager.getChosenYear()
                val chosenMonthStart = sessionManager.getChosenMonthStart()
                val chosenMonthEnd = sessionManager.getChosenMonthEnd()

                val response = apiService.getHomepageData(
                    token = "Token $token",
                    year = chosenYear,
                    monthStart = chosenMonthStart,
                    monthEnd = chosenMonthEnd
                )

                if (response.isSuccessful) {
                    val data = response.body()
                    if (data != null) {
                        _homepageData.value = data
                        // Save the returned filter parameters
                        sessionManager.saveChosenYear(data.chosenyear)
                        sessionManager.saveChosenMonthStart(data.chosenmonthstart)
                        sessionManager.saveChosenMonthEnd(data.chosenmonthend)
                    } else {
                        Log.e("HomeViewModel", "Response body is null")
                        _errorMessage.value = "No data received from server"
                    }
                } else {
                    Log.e("HomeViewModel", "API call failed: ${response.code()} - ${response.message()}")
                    _errorMessage.value = "Failed to load data: ${response.message()}"
                }
            } catch (e: Exception) {
                Log.e("HomeViewModel", "Error loading homepage data", e)
                _errorMessage.value = "Network error: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Updates filter parameters and reloads data.
     */
    fun updateFilters(year: Int?, monthStart: Int?, monthEnd: Int?) {
        // Save the new filter parameters
        year?.let { sessionManager.saveChosenYear(it) }
        monthStart?.let { sessionManager.saveChosenMonthStart(it) }
        monthEnd?.let { sessionManager.saveChosenMonthEnd(it) }

        // Reload data with new filters
        loadHomepageData()
    }

    /**
     * Clears the month end filter and reloads data.
     */
    fun clearMonthEndFilter() {
        // Clear the month end filter parameter
        sessionManager.clearChosenMonthEnd()

        // Reload data with cleared month end filter
        loadHomepageData()
    }

    /**
     * Refreshes homepage data.
     */
    fun refreshHomepageData() {
        loadHomepageData()
    }

    /**
     * Deletes a transaction by its short UUID and refreshes the homepage data.
     */
    fun deleteTransaction(shortUuid: String) {
        val token = sessionManager.getAuthToken()
        if (token == null) {
            Log.e("HomeViewModel", "No auth token available for delete")
            _errorMessage.value = "Authentication token not available"
            return
        }

        viewModelScope.launch {
            try {
                val result = transactionRepository.deleteTransaction(token, shortUuid)
                if (result.isSuccess) {
                    Log.d("HomeViewModel", "Transaction deleted successfully")
                    _successMessage.value = "تراکنش حذف شد"
                    // Refresh homepage data after successful deletion
                    refreshHomepageData()
                } else {
                    Log.e("HomeViewModel", "Failed to delete transaction: ${result.exceptionOrNull()?.message}")
                    _errorMessage.value = "خطا در حذف تراکنش"
                }
            } catch (e: Exception) {
                Log.e("HomeViewModel", "Error deleting transaction", e)
                _errorMessage.value = "Error deleting transaction: ${e.message}"
            }
        }
    }

    /**
     * Deletes an event by its short UUID.
     *
     * @param shortUuid The short UUID of the event to delete.
     */
    fun deleteEvent(shortUuid: String) {
        val token = sessionManager.getAuthToken()
        if (token == null) {
            Log.e("HomeViewModel", "No auth token available")
            _errorMessage.value = "Authentication token not available"
            return
        }

        viewModelScope.launch {
            try {
                val result = eventRepository.deleteEvent(token, shortUuid)
                if (result.isSuccess) {
                    Log.d("HomeViewModel", "Event deleted successfully")
                    _successMessage.value = "رویداد حذف شد"
                    // Refresh homepage data after successful deletion
                    refreshHomepageData()
                } else {
                    Log.e("HomeViewModel", "Failed to delete event: ${result.exceptionOrNull()?.message}")
                    _errorMessage.value = "خطا در حذف رویداد"
                }
            } catch (e: Exception) {
                Log.e("HomeViewModel", "Error deleting event", e)
                _errorMessage.value = "Error deleting event: ${e.message}"
            }
        }
    }


}
