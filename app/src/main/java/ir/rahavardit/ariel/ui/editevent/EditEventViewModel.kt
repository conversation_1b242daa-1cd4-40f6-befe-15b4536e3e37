package ir.rahavardit.ariel.ui.editevent

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.NewEventRequest
import ir.rahavardit.ariel.data.repository.EventRepository
import kotlinx.coroutines.launch

class EditEventViewModel(private val token: String) : ViewModel() {

    private val eventRepository = EventRepository()

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _eventUpdateResult = MutableLiveData<EventUpdateResult>()
    val eventUpdateResult: LiveData<EventUpdateResult> = _eventUpdateResult

    sealed class EventUpdateResult {
        object Success : EventUpdateResult()
        data class Error(val errorMessage: String) : EventUpdateResult()
    }

    fun updateEvent(
        token: String,
        shortUuid: String,
        title: String,
        date: String
    ) {
        viewModelScope.launch {
            _isLoading.value = true

            val request = NewEventRequest(
                title = title,
                date = date
            )

            eventRepository.updateEvent(token, shortUuid, request).fold(
                onSuccess = {
                    _eventUpdateResult.value = EventUpdateResult.Success
                },
                onFailure = { exception ->
                    _eventUpdateResult.value = EventUpdateResult.Error(
                        exception.message ?: "Failed to update event"
                    )
                }
            )

            _isLoading.value = false
        }
    }
}
